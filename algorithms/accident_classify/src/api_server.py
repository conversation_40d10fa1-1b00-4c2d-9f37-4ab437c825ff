#!/usr/bin/env python3
"""
交通事故分类算法 - API服务器 (v2.0统一响应格式)
提供RESTful API接口用于交通事故类型分类
"""

import os
import io
import time
from typing import List, Optional
import configparser
from datetime import datetime

import uvicorn
import numpy as np
from fastapi import FastAPI, File, UploadFile, HTTPException, Form
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from PIL import Image

# 导入推理引擎
from inference_engine import AccidentClassificationEngine
from logger_config import get_logger

# 导入统一响应模型
from unified_models import (
    UnifiedResponse,
    ClassificationData,
    BatchClassificationData,
    Metadata,
    ModelInfo,
    HardwareInfo,
    create_success_response,
    create_error_response,
    convert_legacy_classification_to_v2,
    get_severity_score,
    assess_risk_level
)

# 初始化日志
logger = get_logger()

# 创建FastAPI应用
app = FastAPI(
    title="交通事故分类算法API v2.0",
    description="基于深度学习的交通事故类型分类API服务 - 统一响应格式v2.0",
    version="2.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局推理引擎实例
engine: Optional[AccidentClassificationEngine] = None


def load_config():
    """加载配置文件"""
    config = configparser.ConfigParser()
    config_file = "config.ini"
    
    try:
        config.read(config_file, encoding='utf-8')
        conf_threshold = config.getfloat('CLASSIFICATION', 'confidence_threshold', fallback=0.5)
        batch_size = config.getint('CLASSIFICATION', 'batch_size', fallback=8)
        return conf_threshold, batch_size
    except Exception as e:
        logger.error(f"读取配置文件失败: {e}")
        return 0.5, 8


@app.on_event("startup")
async def startup_event():
    """应用启动时初始化推理引擎"""
    global engine
    try:
        logger.info("正在初始化交通事故分类引擎...")
        config_path = "config.ini"
        engine = AccidentClassificationEngine(config_path)
        logger.info("交通事故分类引擎初始化完成")
    except Exception as e:
        logger.error(f"推理引擎初始化失败: {e}")
        raise e


@app.get("/api/v1/health", response_model=UnifiedResponse)
async def health_check():
    """健康检查接口 - v2.0格式"""
    start_time = time.time()

    health_data = {
        "service": "交通事故分类算法",
        "status": "healthy",
        "engine_loaded": engine is not None
    }

    metadata = Metadata(
        processing_time_ms=(time.time() - start_time) * 1000,
        image_shape=[0, 0, 0],
        timestamp_utc=datetime.utcnow().isoformat() + "Z",
        model_info=ModelInfo(
            name="accident_classification",
            version="2.0.0"
        ),
        hardware_info=HardwareInfo(
            device="cpu"
        )
    )

    return create_success_response(
        data=health_data,
        metadata=metadata
    )


@app.get("/api/v1/info", response_model=UnifiedResponse)
async def get_algorithm_info():
    """获取算法信息 - v2.0格式"""
    start_time = time.time()

    algorithm_info = {
        "algorithm_name": "交通事故分类算法",
        "algorithm_version": "2.0.0",
        "algorithm_type": "图像分类",
        "description": "基于深度学习的交通事故类型分类算法，支持5种严重程度的自动识别",
        "capabilities": {
            "input_modes": ["file", "batch"],
            "supported_formats": ["jpg", "jpeg", "png", "bmp"],
            "max_file_size_mb": 100,
            "concurrent_requests": 3
        },
        "model_info": {
            "framework": "PyTorch",
            "model_file": "accident_classifier.pt",
            "input_size": [224, 224],
            "classes": [
                "轻微事故",
                "一般事故",
                "重大事故",
                "特大事故",
                "非事故"
            ]
        },
        "features": [
            "事故类型分类",
            "严重程度评估",
            "置信度评分",
            "批量处理",
            "风险等级评估"
        ]
    }

    metadata = Metadata(
        processing_time_ms=(time.time() - start_time) * 1000,
        image_shape=[0, 0, 0],
        timestamp_utc=datetime.utcnow().isoformat() + "Z",
        model_info=ModelInfo(
            name="accident_classification",
            version="2.0.0"
        )
    )

    return create_success_response(
        data=algorithm_info,
        metadata=metadata
    )


@app.post("/api/v1/detect", response_model=UnifiedResponse)
async def detect_accident(
    file: UploadFile = File(...),
    return_all_scores: bool = Form(default=True)
):
    """标准检测接口 - v2.0格式（统一接口规范）"""
    start_time = time.time()

    try:
        if not engine:
            return create_error_response(
                error_code="ENGINE_NOT_INITIALIZED",
                error_message="推理引擎未初始化",
                metadata=Metadata(
                    processing_time_ms=(time.time() - start_time) * 1000,
                    image_shape=[0, 0, 0],
                    timestamp_utc=datetime.utcnow().isoformat() + "Z"
                )
            )

        # 验证文件类型
        if not file.content_type.startswith('image/'):
            return create_error_response(
                error_code="INVALID_FILE_FORMAT",
                error_message="不支持的文件格式",
                error_details="仅支持图片文件",
                metadata=Metadata(
                    processing_time_ms=(time.time() - start_time) * 1000,
                    image_shape=[0, 0, 0],
                    timestamp_utc=datetime.utcnow().isoformat() + "Z"
                )
            )

        # 读取上传的图像
        image_data = await file.read()
        image = Image.open(io.BytesIO(image_data))

        # 转换为numpy数组
        image_array = np.array(image)

        # 执行推理
        legacy_results = engine.predict(image_array, return_all_scores=return_all_scores)

        # 添加严重程度信息
        severity_score = get_severity_score(legacy_results['class_name'])
        legacy_results['severity_score'] = severity_score
        legacy_results['risk_level'] = assess_risk_level(severity_score)

        # 转换为v2.0格式
        classification_data = convert_legacy_classification_to_v2(legacy_results)

        # 创建元数据
        processing_time = (time.time() - start_time) * 1000
        metadata = Metadata(
            processing_time_ms=processing_time,
            image_shape=[image_array.shape[0], image_array.shape[1], image_array.shape[2] if len(image_array.shape) > 2 else 3],
            timestamp_utc=datetime.utcnow().isoformat() + "Z",
            model_info=ModelInfo(
                name="accident_classification",
                version="2.0.0"
            ),
            hardware_info=HardwareInfo(
                device="cpu"
            )
        )

        return create_success_response(
            data=classification_data,
            metadata=metadata
        )

    except Exception as e:
        logger.error(f"事故检测失败: {e}")
        return create_error_response(
            error_code="DETECTION_ERROR",
            error_message="事故检测失败",
            error_details=str(e),
            metadata=Metadata(
                processing_time_ms=(time.time() - start_time) * 1000,
                image_shape=[0, 0, 0],
                timestamp_utc=datetime.utcnow().isoformat() + "Z"
            )
        )


@app.post("/api/v1/classify", response_model=UnifiedResponse)
async def classify_accident(
    file: UploadFile = File(...),
    return_all_scores: bool = Form(default=True)
):
    """事故分类接口 - v2.0格式"""
    start_time = time.time()

    try:
        if not engine:
            return create_error_response(
                error_code="ENGINE_NOT_INITIALIZED",
                error_message="推理引擎未初始化",
                metadata=Metadata(
                    processing_time_ms=(time.time() - start_time) * 1000,
                    image_shape=[0, 0, 0],
                    timestamp_utc=datetime.utcnow().isoformat() + "Z"
                )
            )

        # 验证文件类型
        if not file.content_type.startswith('image/'):
            return create_error_response(
                error_code="INVALID_FILE_FORMAT",
                error_message="不支持的文件格式",
                error_details="仅支持图片文件",
                metadata=Metadata(
                    processing_time_ms=(time.time() - start_time) * 1000,
                    image_shape=[0, 0, 0],
                    timestamp_utc=datetime.utcnow().isoformat() + "Z"
                )
            )

        # 读取上传的图像
        image_data = await file.read()
        image = Image.open(io.BytesIO(image_data))

        # 转换为numpy数组
        image_array = np.array(image)

        # 执行推理
        legacy_results = engine.predict(image_array, return_all_scores=return_all_scores)

        # 添加严重程度信息
        severity_score = get_severity_score(legacy_results['class_name'])
        legacy_results['severity_score'] = severity_score
        legacy_results['risk_level'] = assess_risk_level(severity_score)

        # 转换为v2.0格式
        classification_data = convert_legacy_classification_to_v2(legacy_results)

        # 创建元数据
        processing_time = (time.time() - start_time) * 1000
        metadata = Metadata(
            processing_time_ms=processing_time,
            image_shape=[image_array.shape[0], image_array.shape[1], image_array.shape[2] if len(image_array.shape) > 2 else 3],
            timestamp_utc=datetime.utcnow().isoformat() + "Z",
            model_info=ModelInfo(
                name="accident_classification",
                version="2.0.0"
            ),
            hardware_info=HardwareInfo(
                device="cpu"
            )
        )

        return create_success_response(
            data=classification_data,
            metadata=metadata
        )

    except Exception as e:
        logger.error(f"事故分类失败: {e}")
        return create_error_response(
            error_code="CLASSIFICATION_ERROR",
            error_message="事故分类失败",
            error_details=str(e),
            metadata=Metadata(
                processing_time_ms=(time.time() - start_time) * 1000,
                image_shape=[0, 0, 0],
                timestamp_utc=datetime.utcnow().isoformat() + "Z"
            )
        )


@app.post("/api/v1/classify_batch", response_model=UnifiedResponse)
async def classify_batch(files: List[UploadFile] = File(...)):
    """批量分类接口 - v2.0格式"""
    start_time = time.time()

    try:
        if not engine:
            return create_error_response(
                error_code="ENGINE_NOT_INITIALIZED",
                error_message="推理引擎未初始化",
                metadata=Metadata(
                    processing_time_ms=(time.time() - start_time) * 1000,
                    image_shape=[0, 0, 0],
                    timestamp_utc=datetime.utcnow().isoformat() + "Z"
                )
            )

        batch_results = []
        class_counts = {}
        total_confidence = 0.0
        severity_scores = []

        for i, file in enumerate(files):
            try:
                # 读取图像
                image_data = await file.read()
                image = Image.open(io.BytesIO(image_data))
                image_array = np.array(image)

                # 执行推理
                legacy_result = engine.predict(image_array, return_all_scores=True)

                # 添加严重程度信息
                severity_score = get_severity_score(legacy_result['predicted_class'])
                legacy_result['severity_score'] = severity_score
                legacy_result['risk_level'] = assess_risk_level(severity_score)

                # 转换为v2.0格式
                classification_data = convert_legacy_classification_to_v2(legacy_result)

                batch_results.append({
                    'filename': file.filename,
                    'index': i,
                    'classification': classification_data.classification.model_dump(),
                    'summary': classification_data.summary.model_dump()
                })

                # 统计信息
                predicted_class = legacy_result['predicted_class']
                class_counts[predicted_class] = class_counts.get(predicted_class, 0) + 1
                total_confidence += legacy_result['confidence']
                severity_scores.append(severity_score)

            except Exception as e:
                batch_results.append({
                    'filename': file.filename,
                    'index': i,
                    'error': str(e),
                    'classification': None,
                    'summary': None
                })

        # 计算批量统计
        successful_count = len([r for r in batch_results if 'error' not in r])
        avg_confidence = total_confidence / successful_count if successful_count > 0 else 0.0
        avg_severity = sum(severity_scores) / len(severity_scores) if severity_scores else 0.0

        batch_data = BatchClassificationData(
            batch_id=f"batch_{int(time.time())}",
            total_files=len(files),
            results=batch_results,
            summary={
                "successful_classifications": successful_count,
                "failed_classifications": len(files) - successful_count,
                "class_distribution": class_counts,
                "average_confidence": avg_confidence,
                "average_severity": avg_severity,
                "risk_assessment": assess_risk_level(int(avg_severity))
            }
        )

        metadata = Metadata(
            processing_time_ms=(time.time() - start_time) * 1000,
            image_shape=[0, 0, 0],
            timestamp_utc=datetime.utcnow().isoformat() + "Z",
            model_info=ModelInfo(
                name="accident_classification",
                version="2.0.0"
            )
        )

        return create_success_response(
            data=batch_data,
            metadata=metadata
        )

    except Exception as e:
        logger.error(f"批量分类失败: {e}")
        return create_error_response(
            error_code="BATCH_CLASSIFICATION_ERROR",
            error_message="批量分类失败",
            error_details=str(e),
            metadata=Metadata(
                processing_time_ms=(time.time() - start_time) * 1000,
                image_shape=[0, 0, 0],
                timestamp_utc=datetime.utcnow().isoformat() + "Z"
            )
        )


@app.get("/api/v1/classes", response_model=UnifiedResponse)
async def get_supported_classes():
    """获取支持的分类类别 - v2.0格式"""
    start_time = time.time()

    try:
        if not engine:
            return create_error_response(
                error_code="ENGINE_NOT_INITIALIZED",
                error_message="推理引擎未初始化",
                metadata=Metadata(
                    processing_time_ms=(time.time() - start_time) * 1000,
                    image_shape=[0, 0, 0],
                    timestamp_utc=datetime.utcnow().isoformat() + "Z"
                )
            )

        classes_data = {
            "classes": engine.class_names,
            "num_classes": len(engine.class_names),
            "class_descriptions": {
                "轻微事故": "无人员伤亡的轻微碰撞事故",
                "一般事故": "有轻微人员伤亡的交通事故",
                "重大事故": "有重大人员伤亡或财产损失的事故",
                "特大事故": "造成重大人员伤亡的严重事故",
                "非事故": "正常交通场景，无事故发生"
            },
            "severity_levels": {
                "非事故": 0,
                "轻微事故": 1,
                "一般事故": 2,
                "重大事故": 3,
                "特大事故": 4
            },
            "risk_levels": {
                0: "无风险",
                1: "低风险",
                2: "中风险",
                3: "高风险",
                4: "极高风险"
            }
        }

        metadata = Metadata(
            processing_time_ms=(time.time() - start_time) * 1000,
            image_shape=[0, 0, 0],
            timestamp_utc=datetime.utcnow().isoformat() + "Z",
            model_info=ModelInfo(
                name="accident_classification",
                version="2.0.0"
            )
        )

        return create_success_response(
            data=classes_data,
            metadata=metadata
        )

    except Exception as e:
        logger.error(f"获取类别信息失败: {e}")
        return create_error_response(
            error_code="GET_CLASSES_ERROR",
            error_message="获取类别信息失败",
            error_details=str(e),
            metadata=Metadata(
                processing_time_ms=(time.time() - start_time) * 1000,
                image_shape=[0, 0, 0],
                timestamp_utc=datetime.utcnow().isoformat() + "Z"
            )
        )


@app.post("/api/v1/detect", response_model=UnifiedResponse)
async def detect_accident(
    file: UploadFile = File(...),
    return_all_scores: bool = Form(default=True)
):
    """标准检测接口 - v2.0格式（与classify接口功能相同，为了符合平台规范）"""
    # 直接调用classify接口，保持功能一致性
    return await classify_accident(file, return_all_scores)


if __name__ == "__main__":
    # 启动API服务器 - v2.0
    logger.info("启动交通事故分类算法API服务器 v2.0")
    uvicorn.run(
        "api_server:app",
        host="0.0.0.0",
        port=8003,
        reload=False,
        log_level="info"
    )
