"""
统一响应格式模型 - v2.0规范
用于交通事故分类算法
"""

import json
from typing import List, Dict, Any, Optional, Union
from pydantic import BaseModel, Field
from datetime import datetime


class Classification(BaseModel):
    """单个分类结果 - 符合v2.0统一规范"""
    # 必选字段
    label: str = Field(..., description="分类结果标签")
    confidence: float = Field(..., ge=0.0, le=1.0, description="分类结果的置信度")
    class_id: Optional[int] = Field(None, description="类别的数字ID")
    
    # 扩展属性 - 用于容纳算法特有数据
    attributes: Optional[Dict[str, Any]] = Field(default_factory=dict, description="算法特有的扩展数据")


class ClassificationSummary(BaseModel):
    """分类结果摘要"""
    predicted_class: str = Field(..., description="预测的主要类别")
    confidence: float = Field(..., description="主要类别的置信度")
    severity_score: Optional[int] = Field(None, description="严重程度分数")
    all_scores: Optional[Dict[str, float]] = Field(None, description="所有类别的分数")


class ModelInfo(BaseModel):
    """模型信息"""
    name: str = Field(..., description="模型名称")
    version: str = Field(..., description="模型版本")


class HardwareInfo(BaseModel):
    """硬件信息"""
    device: str = Field(..., description="运行设备")
    memory_used_mb: Optional[float] = Field(None, description="内存使用量(MB)")


class Metadata(BaseModel):
    """响应元数据"""
    processing_time_ms: float = Field(..., description="处理耗时(毫秒)")
    image_shape: List[int] = Field(..., description="图像尺寸 [height, width, channels]")
    timestamp_utc: str = Field(..., description="UTC时间戳")
    model_info: Optional[ModelInfo] = Field(None, description="模型信息")
    hardware_info: Optional[HardwareInfo] = Field(None, description="硬件信息")


class ClassificationData(BaseModel):
    """分类数据部分"""
    classification: Classification = Field(..., description="分类结果")
    summary: ClassificationSummary = Field(..., description="结果摘要")
    output_files: Optional[Dict[str, str]] = Field(None, description="输出文件URL")


class BatchClassificationData(BaseModel):
    """批量分类数据"""
    batch_id: str = Field(..., description="批次ID")
    total_files: int = Field(..., description="总文件数")
    results: List[Dict[str, Any]] = Field(..., description="批量结果")
    summary: Dict[str, Any] = Field(..., description="批量摘要")


class ErrorInfo(BaseModel):
    """错误信息"""
    code: str = Field(..., description="错误代码")
    message: str = Field(..., description="用户友好的错误消息")
    details: Optional[str] = Field(None, description="详细错误信息")
    timestamp: str = Field(..., description="错误发生时间")


class UnifiedResponse(BaseModel):
    """统一响应格式 - v2.0规范"""
    success: bool = Field(..., description="是否成功执行")
    error: Optional[ErrorInfo] = Field(None, description="错误信息，成功时为null")
    data: Optional[Union[ClassificationData, BatchClassificationData, Dict[str, Any]]] = Field(None, description="核心业务数据，失败时为null")
    metadata: Metadata = Field(..., description="处理元数据和诊断信息")


# 工具函数
def create_success_response(
    data: Union[ClassificationData, BatchClassificationData, Dict[str, Any]],
    metadata: Metadata
) -> UnifiedResponse:
    """创建成功响应"""
    return UnifiedResponse(
        success=True,
        error=None,
        data=data,
        metadata=metadata
    )


def create_error_response(
    error_code: str,
    error_message: str,
    error_details: Optional[str] = None,
    metadata: Optional[Metadata] = None
) -> UnifiedResponse:
    """创建错误响应"""
    error_info = ErrorInfo(
        code=error_code,
        message=error_message,
        details=error_details,
        timestamp=datetime.utcnow().isoformat() + "Z"
    )
    
    if metadata is None:
        metadata = Metadata(
            processing_time_ms=0.0,
            image_shape=[0, 0, 0],
            timestamp_utc=datetime.utcnow().isoformat() + "Z"
        )
    
    return UnifiedResponse(
        success=False,
        error=error_info,
        data=None,
        metadata=metadata
    )


def convert_legacy_classification_to_v2(legacy_result: Dict[str, Any]) -> ClassificationData:
    """将旧格式的分类结果转换为v2.0格式"""
    
    # 创建分类结果
    classification = Classification(
        label=legacy_result.get('predicted_class', 'unknown'),
        confidence=legacy_result.get('confidence', 0.0),
        class_id=legacy_result.get('class_id'),
        attributes={
            'all_scores': legacy_result.get('all_scores', {}),
            'severity_level': legacy_result.get('severity_level'),
            'risk_assessment': legacy_result.get('risk_assessment')
        }
    )
    
    # 创建摘要
    summary = ClassificationSummary(
        predicted_class=legacy_result.get('predicted_class', 'unknown'),
        confidence=legacy_result.get('confidence', 0.0),
        severity_score=legacy_result.get('severity_score'),
        all_scores=legacy_result.get('all_scores', {})
    )
    
    return ClassificationData(
        classification=classification,
        summary=summary
    )


# 严重程度映射 - 适配2类别模型
SEVERITY_MAPPING = {
    "非事故": 0,
    "事故": 2  # 将"事故"映射为中等严重程度
}


def get_severity_score(class_name: str) -> int:
    """获取严重程度分数"""
    return SEVERITY_MAPPING.get(class_name, 0)


def assess_risk_level(severity_score: int) -> str:
    """评估风险等级"""
    if severity_score == 0:
        return "无风险"
    elif severity_score <= 1:
        return "低风险"
    elif severity_score <= 2:
        return "中风险"
    elif severity_score <= 3:
        return "高风险"
    else:
        return "极高风险"
