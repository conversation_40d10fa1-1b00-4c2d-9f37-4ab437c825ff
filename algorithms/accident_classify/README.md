# 交通事故分类算法 v2.0

基于深度学习的交通事故智能分类API服务，支持5种事故严重程度的自动识别和分类。

**版本**: 2.0.0 - 符合统一AI平台v2.0响应格式规范

## 🚀 快速部署

### Docker部署（推荐）
```bash
# 构建并运行
docker build -t accident-classify:2.0.0 .
docker run -d -p 8003:8003 --name accident-classify accident-classify:2.0.0

# 访问API文档
curl http://localhost:8003/docs
```

### 本地开发
```bash
uv sync && uv run python src/api_server.py
```

## 📋 API接口 (v2.0规范)

| 接口 | 方法 | 功能 | 参数 | 状态 |
|------|------|------|------|------|
| `/api/v1/health` | GET | 健康检查 | - | ✅ v2.0 |
| `/api/v1/info` | GET | 算法信息 | - | ✅ v2.0 |
| `/api/v1/detect` | POST | 标准检测接口 | `file`, `return_all_scores` | ✅ v2.0 |
| `/api/v1/classify` | POST | 事故分类 | `file`, `return_all_scores` | ✅ v2.0 |
| `/api/v1/classify_batch` | POST | 批量分类 | `files[]` | ✅ v2.0 |
| `/api/v1/classes` | GET | 获取支持类别 | - | ✅ v2.0 |

### API调用示例 (v2.0格式)
```bash
# 健康检查
curl http://localhost:8003/api/v1/health

# 获取算法信息
curl http://localhost:8003/api/v1/info

# 获取支持的类别
curl http://localhost:8003/api/v1/classes

# 标准检测接口（推荐）
curl -X POST http://localhost:8003/api/v1/detect \
  -F "file=@accident.jpg" \
  -F "return_all_scores=true"

# 事故分类接口
curl -X POST http://localhost:8003/api/v1/classify \
  -F "file=@accident.jpg" \
  -F "return_all_scores=true"

# 批量分类
curl -X POST http://localhost:8003/api/v1/classify_batch \
  -F "files=@accident1.jpg" \
  -F "files=@accident2.jpg"
```

### v2.0响应格式示例
```json
{
  "success": true,
  "error": null,
  "data": {
    "classification": {
      "label": "重大事故",
      "confidence": 0.89,
      "class_id": 3,
      "attributes": {
        "all_scores": {
          "非事故": 0.02,
          "轻微事故": 0.05,
          "一般事故": 0.04,
          "重大事故": 0.89,
          "特大事故": 0.00
        },
        "severity_level": "重大事故",
        "risk_assessment": "高风险"
      }
    },
    "summary": {
      "predicted_class": "重大事故",
      "confidence": 0.89,
      "severity_score": 3,
      "all_scores": {...}
    }
  },
  "metadata": {
    "processing_time_ms": 156.7,
    "image_shape": [480, 640, 3],
    "timestamp_utc": "2025-07-30T12:55:00.123Z",
    "model_info": {
      "name": "accident_classification",
      "version": "2.0.0"
    },
    "hardware_info": {
      "device": "cpu"
    }
  }
}
```

### 响应格式
```json
{
  "success": true,
  "message": "分类完成，预测类别: 重大事故",
  "data": {
    "predicted_class": "重大事故",
    "confidence": 0.89,
    "all_predictions": {
      "轻微事故": 0.05,
      "一般事故": 0.03,
      "重大事故": 0.89,
      "特大事故": 0.02,
      "非事故": 0.01
    },
    "processing_time": 0.15
  }
}
```

## 🎯 分类类别

| 类别 | 说明 | 特征 |
|------|------|------|
| 轻微事故 | 无人员伤亡 | 轻微碰撞、刮擦 |
| 一般事故 | 轻微人员伤亡 | 有伤者但不严重 |
| 重大事故 | 重大人员伤亡 | 严重伤亡或财产损失 |
| 特大事故 | 特别严重事故 | 重大人员伤亡 |
| 非事故 | 正常交通场景 | 无事故发生 |

## ⚙️ 配置参数

编辑 `config.ini` 调整分类参数：

```ini
[MODEL]
model_path = models/accident_classify_model.onnx
input_size = 224              # 输入图像尺寸
num_classes = 5               # 分类类别数
device = auto                 # 设备选择

[CLASSIFICATION]
confidence_threshold = 0.5    # 置信度阈值
enable_gpu = true            # 启用GPU加速
batch_size = 8               # 批处理大小

[OUTPUT]
save_results = true          # 保存结果
output_format = json         # 输出格式
include_confidence = true    # 包含置信度
```

## 📊 技术规格

- **分类模型**: 基于CNN的深度学习模型 (224x224)
- **分类类别**: 5种事故严重程度
- **推理框架**: ONNX Runtime
- **支持格式**: JPG, PNG, BMP
- **并发处理**: 支持批量API调用
- **GPU加速**: 支持CUDA和OpenVINO

## 🔧 性能调优

| 参数 | 说明 | 推荐值 |
|------|------|--------|
| `input_size` | 分类精度vs速度 | 224(标准) / 128(快速) |
| `confidence_threshold` | 分类置信度 | 0.5-0.8 |
| `batch_size` | 批处理大小 | 4-16 |
| `enable_gpu` | GPU加速 | true(有GPU时) |

## 📈 应用场景

- **交通管理**: 事故严重程度自动评估
- **保险理赔**: 事故等级智能判定
- **应急响应**: 快速事故分类和资源调度
- **数据分析**: 交通事故统计和分析

## 📝 依赖要求

- **运行环境**: Python 3.11+, Docker 20.10+
- **核心依赖**: ONNX Runtime 1.16+, OpenCV 4.8+, FastAPI 0.104+
- **模型文件**: `accident_classify_model.onnx` 需放置在 `models/` 目录
- **硬件要求**: 2GB+ RAM, GPU可选
