# 通用AI算法管理平台开发指南

**版本**: 2.0.7 | **更新**: 2025-07-31

## 目录

1. [项目概述](#1-项目概述)
2. [系统架构](#2-系统架构)
3. [开发规范](#3-开发规范)
4. [开发流程](#4-开发流程)
5. [部署指南](#5-部署指南)
6. [故障排除](#6-故障排除)

## 1. 项目概述

通用AI算法管理平台，提供标准化的算法开发、部署和管理解决方案。

### 核心功能
- **统一API规范**: v2.0响应格式标准
- **容器化部署**: Docker + Docker Compose
- **Web管理界面**: Vue 3 + FastAPI
- **在线测试**: 实时检测 + 性能基准测试
- **智能化体验**: 自动FPS优化 + 多文件上传

### 当前状态
```
算法服务状态:
├── renchefei-v2 (人车非检测)     - 端口8002 ✅ 正常
├── wenzhou-face-v2 (人脸识别)   - 端口8003 ✅ 正常
└── accident-classify-v2 (事故分类) - 端口8004 ⚠️ 模型质量问题

管理平台:
├── 前端服务 - 端口3000 ✅ 正常
└── 后端API - 端口8100 ✅ 正常
```

## 2. 系统架构

### 项目结构
```
algorithm_platform/
├── algorithms/                      # 算法包目录
│   ├── renchefei/                  # 人车非检测
│   ├── wenzhou_face/               # 人脸识别
│   └── accident_classify/          # 事故分类
├── algorithm-platform-manager/     # 管理平台
│   ├── frontend/                   # Vue 3前端
│   └── src/                        # FastAPI后端
└── data/                           # 测试数据
    ├── renchefei/input/
    ├── wenzhou_face/input/
    └── accident_classify/input/
```

### 技术栈
- **前端**: Vue 3 + Element Plus + Vite
- **后端**: Python 3.11 + FastAPI + uv
- **容器**: Docker + Docker Compose
- **数据库**: SQLite (开发) / PostgreSQL (生产)

### 端口分配
| 服务 | 端口 | 说明 |
|------|------|------|
| 算法API | 8002-8099 | 算法容器端口 |
| 管理API | 8100 | 平台后端API |
| 前端 | 3000 | 开发服务器 |
| Nginx | 80/443 | 生产环境 |

## 3. 开发规范

### 3.1 统一响应格式 (v2.0)

所有API必须返回以下结构：

```json
{
  "success": true,
  "error": null,
  "data": { /* 业务数据 */ },
  "metadata": { /* 元数据 */ }
}
```

#### 成功响应示例
```json
{
  "success": true,
  "error": null,
  "data": {
    "detections": [
      {
        "bbox": [132, 98, 308, 336],
        "confidence": 0.8829,
        "label": "face",
        "class_id": 0
      }
    ]
  },
  "metadata": {
    "processing_time_ms": 802.68,
    "timestamp_utc": "2025-07-30T12:55:00.123Z"
  }
}
```

#### 错误响应示例
```json
{
  "success": false,
  "error": {
    "code": "INVALID_FILE_FORMAT",
    "message": "不支持的文件格式"
  },
  "data": null,
  "metadata": {
    "processing_time_ms": 15.2,
    "timestamp_utc": "2025-07-30T12:55:00.123Z"
  }
}
```
### 3.2 必需API端点

| 端点 | 方法 | 功能 |
|------|------|------|
| `/api/v1/health` | GET | 健康检查 |
| `/api/v1/info` | GET | 算法信息 |
| `/api/v1/detect` | POST | 核心功能 |
| `/docs` | GET | API文档 |

#### 健康检查接口
```python
@app.get("/api/v1/health")
async def health_check():
    return create_success_response(
        data={"status": "healthy"},
        metadata={"processing_time_ms": 1.0}
    )
```

#### 算法信息接口
```python
@app.get("/api/v1/info")
async def get_algorithm_info():
    return create_success_response(
        data={
            "algorithm_name": "算法名称",
            "algorithm_version": "2.0.0",
            "supported_formats": ["jpg", "jpeg", "png"]
        }
    )
```

### 3.3 项目结构

```
algorithms/{algorithm_name}/
├── pyproject.toml              # uv项目配置
├── Dockerfile                  # 容器配置
├── src/
│   ├── api_server.py           # FastAPI入口
│   ├── models/unified.py       # 统一响应模型
│   ├── core/algorithm.py       # 算法核心逻辑
│   └── config.py               # 配置管理
├── models/                     # 模型文件
└── tests/                      # 测试脚本
```

### 3.4 实时检测系统

#### 双重检测方案
- **HTTP API**: 5-30FPS，适用于标准检测
- **WebSocket**: 5-60FPS，适用于高频实时检测

#### 智能FPS自动应用
```javascript
const applyRecommendedFPS = (recommendedFPS) => {
  let targetFPS = Math.round(recommendedFPS)
  if (targetFPS < 5) targetFPS = 5
  if (targetFPS > 60) targetFPS = 60
  if (detectionMode !== 'websocket' && targetFPS > 30) {
    targetFPS = 30
  }
  detectionFPS = targetFPS
}
```

#### 性能监控
- 实时FPS计算
- 处理时间统计
- 智能性能等级显示

### 3.5 多文件上传规范

#### 接口参数设计
```json
{
  "file1": "",
  "file2": "",
  "threshold": 0.6
}
```

#### 前端识别逻辑
```javascript
const fileFields = computed(() => {
  const params = JSON.parse(testForm.value.parameters || '{}')
  return Object.entries(params)
    .filter(([key, value]) =>
      key.toLowerCase().includes('file') || value === ''
    )
    .map(([key]) => ({ name: key, label: key }))
})
```

#### 后端处理
```python
@router.post("/test")
async def test_api(
    file1: UploadFile = File(None),
    file2: UploadFile = File(None),
    parameters: str = Form(default="{}")
):
    # 收集文件并排除文件字段参数
    uploaded_files = [(name, file) for name, file in
                     [("file1", file1), ("file2", file2)] if file]
```

### 3.6 Docker容器化

#### 必需标签
```dockerfile
LABEL algorithm.platform="true"
LABEL algorithm.name="算法名称"
LABEL algorithm.type="目标检测"
LABEL algorithm.version="2.0.0"
```

#### Dockerfile模板
```dockerfile
FROM python:3.11-slim
WORKDIR /app

# 安装uv和系统依赖
RUN pip install uv && \
    apt-get update && apt-get install -y curl && \
    rm -rf /var/lib/apt/lists/*

# 安装依赖
COPY pyproject.toml uv.lock ./
RUN uv sync --frozen --no-dev

# 复制代码
COPY src/ ./src/
COPY models/ ./models/

EXPOSE 8000
HEALTHCHECK CMD curl -f http://localhost:8000/api/v1/health
CMD ["uv", "run", "python", "src/api_server.py"]
```

## 4. 开发流程

### 4.1 环境准备

#### 系统要求
- Python 3.11+
- Docker 20.10+
- uv包管理器

#### 安装uv
```bash
# macOS/Linux
curl -LsSf https://astral.sh/uv/install.sh | sh

# 验证
uv --version
```

### 4.2 新算法开发

#### 创建项目
```bash
mkdir algorithms/my_algorithm
cd algorithms/my_algorithm
uv init --name my_algorithm
uv add fastapi uvicorn pydantic pillow numpy
```

#### 项目配置
```toml
[project]
name = "my-algorithm"
version = "2.0.0"
requires-python = ">=3.11"
dependencies = [
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "pydantic>=2.0.0",
    "pillow>=10.0.0",
    "numpy>=1.24.0"
]
```

#### 核心代码实现

**统一响应模型**
```python
from typing import Optional, Any
from pydantic import BaseModel
from datetime import datetime

class UnifiedResponse(BaseModel):
    success: bool
    error: Optional[dict] = None
    data: Optional[Any] = None
    metadata: dict

def create_success_response(data: Any, metadata: dict = None):
    return UnifiedResponse(
        success=True,
        data=data,
        metadata=metadata or {"timestamp_utc": datetime.utcnow().isoformat()}
    )

def create_error_response(code: str, message: str):
    return UnifiedResponse(
        success=False,
        error={"code": code, "message": message},
        data=None,
        metadata={"timestamp_utc": datetime.utcnow().isoformat()}
    )
```

**算法核心类**
```python
import time
from PIL import Image
from typing import List, Dict

class MyAlgorithm:
    def __init__(self, model_path: str = None):
        self.model_path = model_path
        self.load_model()

    def load_model(self):
        # 实现模型加载
        pass

    def process(self, image: Image.Image) -> tuple:
        start_time = time.time()

        # 实现算法逻辑
        results = {
            "detections": [
                {
                    "bbox": [100, 100, 200, 200],
                    "confidence": 0.95,
                    "label": "object",
                    "class_id": 0
                }
            ]
        }

        processing_time = (time.time() - start_time) * 1000
        return results, processing_time
```

**FastAPI服务器**
```python
import time
from datetime import datetime
from fastapi import FastAPI, File, UploadFile
from fastapi.middleware.cors import CORSMiddleware
from PIL import Image
import io

from models.unified import create_success_response, create_error_response
from core.algorithm import MyAlgorithm

app = FastAPI(title="My Algorithm API", version="2.0.0")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_methods=["*"],
    allow_headers=["*"]
)

algorithm = MyAlgorithm()

@app.get("/api/v1/health")
async def health_check():
    return create_success_response(
        data={"status": "healthy"},
        metadata={"processing_time_ms": 1.0}
    )

@app.get("/api/v1/info")
async def get_algorithm_info():
    return create_success_response(
        data={
            "algorithm_name": "我的算法",
            "algorithm_version": "2.0.0",
            "supported_formats": ["jpg", "jpeg", "png"]
        }
    )

@app.post("/api/v1/detect")
async def detect_objects(file: UploadFile = File(...)):
    try:
        if not file.content_type.startswith('image/'):
            return create_error_response(
                "INVALID_FILE_FORMAT",
                "不支持的文件格式"
            )

        image_data = await file.read()
        image = Image.open(io.BytesIO(image_data))
        results, processing_time = algorithm.process(image)

        return create_success_response(
            data=results,
            metadata={"processing_time_ms": processing_time}
        )

    except Exception as e:
        return create_error_response(
            "PROCESSING_ERROR",
            f"处理失败: {str(e)}"
        )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
```

### 4.3 本地测试

#### 启动服务
```bash
cd algorithms/my_algorithm
uv run python src/api_server.py
```

#### 测试API
```bash
# 健康检查
curl http://localhost:8000/api/v1/health

# 测试检测
curl -X POST "http://localhost:8000/api/v1/detect" \
     -F "file=@test_image.jpg"
```

#### 构建容器
```bash
docker build -t my-algorithm:2.0.0 .
docker run -p 8001:8000 my-algorithm:2.0.0
```

## 5. 部署指南

### 5.1 Docker Compose部署

#### 统一部署所有算法
```bash
# 启动所有算法容器
docker-compose -f docker-compose-algorithms.yml up -d

# 查看状态
docker ps

# 验证健康状态
curl http://localhost:8002/api/v1/health  # 人车非检测
curl http://localhost:8003/api/v1/health  # 人脸识别
```

#### 增量部署新算法
```bash
# 只启动新算法
docker-compose -f docker-compose-algorithms.yml up -d new-algorithm

# 验证部署
curl http://localhost:8005/api/v1/health
```

### 5.2 管理平台部署

#### 启动管理平台
```bash
cd algorithm-platform-manager

# 启动后端
uv run python src/main.py

# 启动前端 (新终端)
cd frontend
npm run dev
```

#### 访问地址
- 管理界面: http://localhost:3000
- API文档: http://localhost:8100/docs

### 5.3 生产环境配置

#### Docker Compose配置
```yaml
version: '3.8'
services:
  my-algorithm:
    build: .
    ports:
      - "8001:8000"
    volumes:
      - ./models:/app/models:ro
    environment:
      - ENV=production
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
```

#### Nginx反向代理
```nginx
server {
    listen 80;
    client_max_body_size 100M;

    location / {
        proxy_pass http://my-algorithm:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 6. 故障排除

### 6.1 常见问题

#### 容器启动失败
```bash
# 查看容器日志
docker logs container_name

# 检查端口占用
netstat -tulpn | grep 8000

# 检查Docker标签
docker inspect container_name | grep -A 10 "Labels"
```

#### API响应格式错误
确保所有API端点都使用统一响应格式：
```python
@app.get("/api/v1/health")
async def health_check():
    return create_success_response(data={"status": "healthy"})
```

#### 文件上传格式错误
添加智能Content-Type推断：
```python
def get_content_type(filename: str) -> str:
    ext = filename.lower().split('.')[-1]
    if ext in ['jpg', 'jpeg']:
        return 'image/jpeg'
    elif ext == 'png':
        return 'image/png'
    return 'application/octet-stream'
```

### 6.2 性能问题

#### 推理速度慢
- 使用ONNX、TensorRT优化模型
- 支持批量推理
- 减少不必要的图像变换
- 使用GPU加速

#### 内存泄漏
```python
import gc
def process_with_cleanup(image):
    try:
        result = algorithm.process(image)
        return result
    finally:
        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
```

### 6.3 调试工具

#### 健康检查脚本
```python
import requests
import time

def health_check_loop(url, interval=30):
    while True:
        try:
            response = requests.get(f"{url}/api/v1/health", timeout=10)
            if response.status_code == 200:
                print(f"✅ Health check passed at {time.ctime()}")
            else:
                print(f"❌ HTTP {response.status_code}")
        except Exception as e:
            print(f"❌ Health check error: {e}")
        time.sleep(interval)
```



















