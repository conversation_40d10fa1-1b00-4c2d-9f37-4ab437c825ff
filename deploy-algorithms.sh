#!/bin/bash

# 算法平台部署脚本 - v2.0
# 部署三个算法容器：renchefei, wenzhou-face, accident-classify

set -e

echo "🚀 开始部署算法平台 v2.0..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查Docker是否运行
check_docker() {
    echo -e "${BLUE}检查Docker状态...${NC}"
    if ! docker info > /dev/null 2>&1; then
        echo -e "${RED}❌ Docker未运行，请启动Docker后重试${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ Docker运行正常${NC}"
}

# 检查镜像是否存在
check_images() {
    echo -e "${BLUE}检查算法镜像...${NC}"
    
    images=("renchefei:2.0.0" "wenzhou-face:2.0.0" "accident-classify:2.0.0")
    missing_images=()
    
    for image in "${images[@]}"; do
        if ! docker image inspect "$image" > /dev/null 2>&1; then
            missing_images+=("$image")
        else
            echo -e "${GREEN}✅ 镜像存在: $image${NC}"
        fi
    done
    
    if [ ${#missing_images[@]} -ne 0 ]; then
        echo -e "${RED}❌ 缺少以下镜像:${NC}"
        for image in "${missing_images[@]}"; do
            echo -e "${RED}   - $image${NC}"
        done
        echo -e "${YELLOW}请先构建缺少的镜像${NC}"
        exit 1
    fi
}

# 停止现有容器
stop_existing() {
    echo -e "${BLUE}停止现有算法容器...${NC}"
    
    containers=("renchefei-v2" "wenzhou-face-v2" "accident-classify-v2")
    
    for container in "${containers[@]}"; do
        if docker ps -a --format "table {{.Names}}" | grep -q "^$container$"; then
            echo -e "${YELLOW}停止容器: $container${NC}"
            docker stop "$container" > /dev/null 2>&1 || true
            docker rm "$container" > /dev/null 2>&1 || true
        fi
    done
}

# 创建必要目录
create_directories() {
    echo -e "${BLUE}创建数据目录...${NC}"
    
    directories=(
        "data/renchefei/input"
        "data/renchefei/output"
        "data/wenzhou_face/input"
        "data/wenzhou_face/output"
        "data/accident_classify/input"
        "data/accident_classify/output"
        "logs/renchefei"
        "logs/wenzhou_face"
        "logs/accident_classify"
    )
    
    for dir in "${directories[@]}"; do
        mkdir -p "$dir"
        echo -e "${GREEN}✅ 创建目录: $dir${NC}"
    done
}

# 部署算法容器
deploy_algorithms() {
    echo -e "${BLUE}部署算法容器...${NC}"
    
    # 使用Docker Compose部署
    if [ -f "docker-compose-algorithms.yml" ]; then
        echo -e "${BLUE}使用Docker Compose部署...${NC}"
        docker-compose -f docker-compose-algorithms.yml up -d
    else
        echo -e "${YELLOW}Docker Compose文件不存在，使用单独部署...${NC}"
        
        # 人车非检测算法
        echo -e "${BLUE}部署人车非检测算法...${NC}"
        docker run -d \
            --name renchefei-v2 \
            -p 8002:8002 \
            -v "$(pwd)/data/renchefei/input:/app/data/input" \
            -v "$(pwd)/data/renchefei/output:/app/data/output" \
            -v "$(pwd)/logs/renchefei:/app/logs" \
            -e PYTHONPATH=/app \
            -e PYTHONUNBUFFERED=1 \
            --restart unless-stopped \
            renchefei:2.0.0
        
        # 温州人脸识别算法
        echo -e "${BLUE}部署温州人脸识别算法...${NC}"
        docker run -d \
            --name wenzhou-face-v2 \
            -p 8003:8003 \
            -v "$(pwd)/data/wenzhou_face/input:/app/data/input" \
            -v "$(pwd)/data/wenzhou_face/output:/app/data/output" \
            -v "$(pwd)/logs/wenzhou_face:/app/logs" \
            -e PYTHONPATH=/app \
            -e PYTHONUNBUFFERED=1 \
            --restart unless-stopped \
            wenzhou-face:2.0.0
        
        # 交通事故分类算法
        echo -e "${BLUE}部署交通事故分类算法...${NC}"
        docker run -d \
            --name accident-classify-v2 \
            -p 8004:8003 \
            -v "$(pwd)/data/accident_classify/input:/app/data/input" \
            -v "$(pwd)/data/accident_classify/output:/app/data/output" \
            -v "$(pwd)/logs/accident_classify:/app/logs" \
            -e PYTHONPATH=/app \
            -e PYTHONUNBUFFERED=1 \
            --restart unless-stopped \
            accident-classify:2.0.0
    fi
}

# 等待服务启动
wait_for_services() {
    echo -e "${BLUE}等待服务启动...${NC}"
    
    services=(
        "renchefei-v2:8002"
        "wenzhou-face-v2:8003"
        "accident-classify-v2:8004"
    )
    
    for service in "${services[@]}"; do
        container_name=$(echo "$service" | cut -d':' -f1)
        port=$(echo "$service" | cut -d':' -f2)
        
        echo -e "${YELLOW}等待 $container_name 启动...${NC}"
        
        # 等待容器启动
        timeout=60
        while [ $timeout -gt 0 ]; do
            if docker ps --format "table {{.Names}}\t{{.Status}}" | grep "$container_name" | grep -q "Up"; then
                break
            fi
            sleep 2
            timeout=$((timeout - 2))
        done
        
        if [ $timeout -le 0 ]; then
            echo -e "${RED}❌ $container_name 启动超时${NC}"
            continue
        fi
        
        # 等待健康检查
        echo -e "${YELLOW}等待 $container_name 健康检查...${NC}"
        timeout=60
        while [ $timeout -gt 0 ]; do
            if curl -f "http://localhost:$port/api/v1/health" > /dev/null 2>&1; then
                echo -e "${GREEN}✅ $container_name 启动成功 (端口: $port)${NC}"
                break
            fi
            sleep 3
            timeout=$((timeout - 3))
        done
        
        if [ $timeout -le 0 ]; then
            echo -e "${YELLOW}⚠️  $container_name 健康检查超时，但容器已启动${NC}"
        fi
    done
}

# 显示部署状态
show_status() {
    echo -e "\n${GREEN}🎉 算法平台部署完成！${NC}\n"
    
    echo -e "${BLUE}📊 容器状态:${NC}"
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -E "(renchefei-v2|wenzhou-face-v2|accident-classify-v2)"
    
    echo -e "\n${BLUE}🔗 API访问地址:${NC}"
    echo -e "${GREEN}• 人车非检测算法:     http://localhost:8002${NC}"
    echo -e "${GREEN}  - 健康检查:         http://localhost:8002/api/v1/health${NC}"
    echo -e "${GREEN}  - API文档:          http://localhost:8002/docs${NC}"
    
    echo -e "${GREEN}• 温州人脸识别算法:   http://localhost:8003${NC}"
    echo -e "${GREEN}  - 健康检查:         http://localhost:8003/api/v1/health${NC}"
    echo -e "${GREEN}  - API文档:          http://localhost:8003/docs${NC}"
    
    echo -e "${GREEN}• 交通事故分类算法:   http://localhost:8004${NC}"
    echo -e "${GREEN}  - 健康检查:         http://localhost:8004/api/v1/health${NC}"
    echo -e "${GREEN}  - API文档:          http://localhost:8004/docs${NC}"
    
    echo -e "\n${BLUE}📁 数据目录:${NC}"
    echo -e "${GREEN}• 输入数据: ./data/{algorithm_name}/input${NC}"
    echo -e "${GREEN}• 输出数据: ./data/{algorithm_name}/output${NC}"
    echo -e "${GREEN}• 日志文件: ./logs/{algorithm_name}${NC}"
    
    echo -e "\n${BLUE}🛠️  管理命令:${NC}"
    echo -e "${GREEN}• 查看日志: docker logs <container_name>${NC}"
    echo -e "${GREEN}• 停止服务: docker-compose -f docker-compose-algorithms.yml down${NC}"
    echo -e "${GREEN}• 重启服务: docker-compose -f docker-compose-algorithms.yml restart${NC}"
}

# 主函数
main() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}    算法平台 v2.0 部署脚本${NC}"
    echo -e "${BLUE}========================================${NC}\n"
    
    check_docker
    check_images
    stop_existing
    create_directories
    deploy_algorithms
    wait_for_services
    show_status
    
    echo -e "\n${GREEN}🎯 部署完成！所有算法服务已启动并运行${NC}"
}

# 执行主函数
main "$@"
