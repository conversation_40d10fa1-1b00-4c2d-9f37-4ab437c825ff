# Python-generated files
__pycache__/
*.py[oc]
*.pyo
*.pyd
build/
dist/
wheels/
*.egg-info
*.egg
.pytest_cache/
.coverage
.coverage.*
coverage.xml
*.cover
.hypothesis/

# Virtual environments
.venv/
venv/
env/
ENV/
.env
.env.local
.env.*.local

# Model weight files - ignore model files but keep code
*.pt
*.pth
*.onnx
*.pb
*.h5
*.pkl
*.bin
*.safetensors
*.tflite
*.engine
*.weights
*.model

# Model directories (but allow code files inside)
/algorithms/*/models/*.pt
/algorithms/*/models/*.pth
/algorithms/*/models/*.onnx
/algorithms/*/models/*.pb
/algorithms/*/models/*.h5
/algorithms/*/models/*.pkl
/algorithms/*/models/*.bin
/algorithms/*/models/*.safetensors
/algorithms/*/models/*.tflite
/algorithms/*/models/*.engine

# Large data files
*.zip
*.tar.gz
*.rar
*.7z

# Data directories
data/
*/data/
**/data/
logs/
*/logs/
**/logs/

# System files
.DS_Store
*/.DS_Store
**/.DS_Store

# Node.js and frontend
node_modules/
*/node_modules/
**/node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity
.pnpm-debug.log*
.pnpm-store/

# Frontend build outputs
dist/
build/
.vite/
.cache/
.temp/
.tmp/

# Frontend development
.eslintcache
.stylelintcache

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~
.project
.classpath
.settings/

# Docker
.dockerignore
docker-compose.override.yml
.docker/

# Temporary files
*.tmp
*.temp
*.log
*.pid
*.seed
*.pid.lock

# OS generated files
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# Claude AI assistant files
.claude/
