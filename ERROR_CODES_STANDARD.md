# 统一AI平台错误代码标准

## 错误代码分类体系

### 1. 客户端错误 (4xx系列)

#### 文件相关错误 (FILE_*)
| 错误代码 | HTTP状态码 | 描述 | 示例场景 |
|---------|-----------|------|----------|
| `FILE_NOT_PROVIDED` | 400 | 未提供必需的文件 | 检测接口未上传图片 |
| `FILE_INVALID_FORMAT` | 400 | 不支持的文件格式 | 上传了txt文件到图像检测接口 |
| `FILE_TOO_LARGE` | 413 | 文件大小超出限制 | 图片大小超过10MB |
| `FILE_CORRUPTED` | 400 | 文件损坏或无法读取 | 图片文件损坏 |
| `FILE_EMPTY` | 400 | 文件为空 | 上传了0字节文件 |

#### 参数相关错误 (PARAM_*)
| 错误代码 | HTTP状态码 | 描述 | 示例场景 |
|---------|-----------|------|----------|
| `PARAM_MISSING` | 400 | 缺少必需参数 | 未提供threshold参数 |
| `PARAM_INVALID_TYPE` | 400 | 参数类型错误 | threshold传入字符串而非数字 |
| `PARAM_OUT_OF_RANGE` | 400 | 参数值超出有效范围 | threshold设为1.5(>1.0) |
| `PARAM_INVALID_FORMAT` | 400 | 参数格式错误 | JSON格式错误 |

#### 认证授权错误 (AUTH_*)
| 错误代码 | HTTP状态码 | 描述 | 示例场景 |
|---------|-----------|------|----------|
| `AUTH_TOKEN_MISSING` | 401 | 缺少认证令牌 | 未提供API密钥 |
| `AUTH_TOKEN_INVALID` | 401 | 认证令牌无效 | API密钥错误 |
| `AUTH_TOKEN_EXPIRED` | 401 | 认证令牌已过期 | API密钥过期 |
| `AUTH_PERMISSION_DENIED` | 403 | 权限不足 | 无权访问特定算法 |

### 2. 服务器错误 (5xx系列)

#### 算法处理错误 (ALGO_*)
| 错误代码 | HTTP状态码 | 描述 | 示例场景 |
|---------|-----------|------|----------|
| `ALGO_MODEL_NOT_LOADED` | 500 | 算法模型未加载 | 模型文件缺失 |
| `ALGO_PROCESSING_FAILED` | 500 | 算法处理失败 | 推理过程异常 |
| `ALGO_TIMEOUT` | 504 | 算法处理超时 | 处理时间超过30秒 |
| `ALGO_MEMORY_ERROR` | 500 | 内存不足 | GPU显存不足 |
| `ALGO_UNSUPPORTED_INPUT` | 422 | 输入数据不支持 | 图片尺寸过小/过大 |

#### 系统资源错误 (SYS_*)
| 错误代码 | HTTP状态码 | 描述 | 示例场景 |
|---------|-----------|------|----------|
| `SYS_RESOURCE_EXHAUSTED` | 503 | 系统资源耗尽 | CPU/内存使用率过高 |
| `SYS_DISK_FULL` | 507 | 磁盘空间不足 | 临时文件无法写入 |
| `SYS_NETWORK_ERROR` | 502 | 网络连接错误 | 外部服务不可达 |
| `SYS_DATABASE_ERROR` | 500 | 数据库连接错误 | 数据库服务异常 |

#### 配置错误 (CONFIG_*)
| 错误代码 | HTTP状态码 | 描述 | 示例场景 |
|---------|-----------|------|----------|
| `CONFIG_INVALID` | 500 | 配置文件无效 | 配置参数错误 |
| `CONFIG_MISSING` | 500 | 配置文件缺失 | 找不到配置文件 |

### 3. 业务逻辑错误 (BIZ_*)

| 错误代码 | HTTP状态码 | 描述 | 示例场景 |
|---------|-----------|------|----------|
| `BIZ_NO_DETECTION_RESULT` | 200 | 未检测到目标对象 | 图片中无人脸 |
| `BIZ_CONFIDENCE_TOO_LOW` | 200 | 检测置信度过低 | 所有检测结果低于阈值 |
| `BIZ_RATE_LIMIT_EXCEEDED` | 429 | 请求频率超限 | 超过每分钟请求限制 |

## 错误处理最佳实践

### 1. 错误响应格式
```json
{
  "success": false,
  "error": {
    "code": "FILE_INVALID_FORMAT",
    "message": "不支持的文件格式，仅支持 jpg, jpeg, png",
    "details": {
      "received_format": "gif",
      "supported_formats": ["jpg", "jpeg", "png"]
    }
  },
  "data": null,
  "metadata": {
    "processing_time_ms": 15.2,
    "timestamp_utc": "2025-07-31T12:55:00.123Z",
    "request_id": "req_123456789"
  }
}
```

### 2. 错误处理代码模板
```python
from enum import Enum
from typing import Optional, Dict, Any

class ErrorCode(Enum):
    # 文件相关
    FILE_NOT_PROVIDED = "FILE_NOT_PROVIDED"
    FILE_INVALID_FORMAT = "FILE_INVALID_FORMAT"
    FILE_TOO_LARGE = "FILE_TOO_LARGE"
    FILE_CORRUPTED = "FILE_CORRUPTED"
    
    # 参数相关
    PARAM_MISSING = "PARAM_MISSING"
    PARAM_INVALID_TYPE = "PARAM_INVALID_TYPE"
    PARAM_OUT_OF_RANGE = "PARAM_OUT_OF_RANGE"
    
    # 算法相关
    ALGO_MODEL_NOT_LOADED = "ALGO_MODEL_NOT_LOADED"
    ALGO_PROCESSING_FAILED = "ALGO_PROCESSING_FAILED"
    ALGO_TIMEOUT = "ALGO_TIMEOUT"

def create_error_response(
    code: ErrorCode, 
    message: str, 
    details: Optional[Dict[str, Any]] = None,
    http_status: int = 500
):
    error_data = {
        "code": code.value,
        "message": message
    }
    if details:
        error_data["details"] = details
    
    return UnifiedResponse(
        success=False,
        error=error_data,
        data=None,
        metadata={
            "processing_time_ms": 0,
            "timestamp_utc": datetime.utcnow().isoformat(),
            "request_id": generate_request_id()
        }
    ), http_status

# 使用示例
@app.post("/api/v1/detect")
async def detect_objects(file: UploadFile = File(...)):
    try:
        if not file:
            return create_error_response(
                ErrorCode.FILE_NOT_PROVIDED,
                "请上传图片文件",
                http_status=400
            )
        
        if not file.content_type.startswith('image/'):
            return create_error_response(
                ErrorCode.FILE_INVALID_FORMAT,
                "不支持的文件格式，仅支持 jpg, jpeg, png",
                details={
                    "received_format": file.content_type,
                    "supported_formats": ["image/jpeg", "image/png"]
                },
                http_status=400
            )
            
    except Exception as e:
        return create_error_response(
            ErrorCode.ALGO_PROCESSING_FAILED,
            f"算法处理失败: {str(e)}",
            details={"exception_type": type(e).__name__}
        )
```

### 3. 日志记录规范
```python
import logging
import uuid

def generate_request_id():
    return f"req_{uuid.uuid4().hex[:12]}"

def log_error(error_code: ErrorCode, message: str, request_id: str, **kwargs):
    logging.error(
        f"[{request_id}] {error_code.value}: {message}",
        extra={"error_code": error_code.value, "request_id": request_id, **kwargs}
    )

# 使用示例
request_id = generate_request_id()
try:
    # 处理逻辑
    pass
except Exception as e:
    log_error(
        ErrorCode.ALGO_PROCESSING_FAILED,
        str(e),
        request_id,
        file_name=file.filename,
        file_size=len(await file.read())
    )
```

### 4. 前端错误处理指南
```javascript
// 错误处理工具函数
const handleApiError = (error) => {
  const errorCode = error.response?.data?.error?.code
  const errorMessage = error.response?.data?.error?.message
  
  switch(errorCode) {
    case 'FILE_INVALID_FORMAT':
      ElMessage.error('请上传正确格式的图片文件')
      break
    case 'FILE_TOO_LARGE':
      ElMessage.error('文件大小不能超过10MB')
      break
    case 'ALGO_TIMEOUT':
      ElMessage.error('处理超时，请稍后重试')
      break
    default:
      ElMessage.error(errorMessage || '处理失败，请稍后重试')
  }
}

// API调用示例
const callDetectionAPI = async (file) => {
  try {
    const response = await axios.post('/api/v1/detect', formData)
    return response.data
  } catch (error) {
    handleApiError(error)
    throw error
  }
}
```
