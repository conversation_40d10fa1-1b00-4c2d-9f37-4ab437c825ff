#!/usr/bin/env python3
"""
WebSocket连接测试脚本
用于验证WebSocket实时检测功能是否正常工作
"""

import asyncio
import websockets
import json
import base64
from PIL import Image
import io

async def test_websocket_connection():
    """测试WebSocket连接和基本通信"""
    uri = "ws://localhost:8100/api/v1/ws/realtime-detect/wenzhou-face-v2"
    
    try:
        print(f"🔌 连接到WebSocket: {uri}")
        async with websockets.connect(uri) as websocket:
            print("✅ WebSocket连接成功建立")
            
            # 等待连接确认消息
            response = await websocket.recv()
            data = json.loads(response)
            print(f"📨 收到连接确认: {data}")
            
            # 发送心跳测试
            print("💓 发送心跳测试...")
            ping_message = {
                "type": "ping",
                "timestamp": 1234567890
            }
            await websocket.send(json.dumps(ping_message))
            
            # 等待心跳响应
            response = await websocket.recv()
            data = json.loads(response)
            print(f"💓 收到心跳响应: {data}")
            
            # 创建一个测试图片
            print("🖼️ 创建测试图片...")
            test_image = Image.new('RGB', (640, 480), color='red')
            img_buffer = io.BytesIO()
            test_image.save(img_buffer, format='JPEG')
            img_data = img_buffer.getvalue()
            img_base64 = base64.b64encode(img_data).decode('utf-8')
            
            # 发送测试帧
            print("📸 发送测试视频帧...")
            frame_message = {
                "type": "frame",
                "frame": f"data:image/jpeg;base64,{img_base64}",
                "parameters": {
                    "extract_features": False,
                    "assess_quality": False,
                    "confidence_threshold": 0.7,
                    "max_num_faces": 0,
                    "nms_threshold": 0.4
                }
            }
            await websocket.send(json.dumps(frame_message))
            
            # 等待检测结果
            print("⏳ 等待检测结果...")
            response = await websocket.recv()
            data = json.loads(response)
            print(f"🎯 收到检测结果: {data}")
            
            print("✅ WebSocket测试完成！")
            
    except websockets.exceptions.ConnectionClosed:
        print("❌ WebSocket连接被关闭")
    except Exception as e:
        print(f"❌ WebSocket测试失败: {e}")

if __name__ == "__main__":
    print("🚀 开始WebSocket连接测试...")
    asyncio.run(test_websocket_connection())
