#!/usr/bin/env python3
"""
WebSocket实时检测API
提供基于WebSocket的实时视频流检测功能
"""

import json
import asyncio
import logging
from typing import Dict, Any
from io import BytesIO

from fastapi import APIRouter, WebSocket, WebSocketDisconnect, HTTPException
from PIL import Image
import aiohttp
import requests

from core.docker_manager import DockerManager

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter()

# Docker管理器实例
docker_manager = DockerManager()

# 容器信息缓存
container_cache = {}
cache_timestamp = 0
CACHE_DURATION = 30  # 缓存30秒

class ConnectionManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
    
    async def connect(self, websocket: WebSocket, client_id: str):
        """接受WebSocket连接"""
        await websocket.accept()
        self.active_connections[client_id] = websocket
        logger.info(f"WebSocket连接已建立: {client_id}")
    
    def disconnect(self, client_id: str):
        """断开WebSocket连接"""
        if client_id in self.active_connections:
            del self.active_connections[client_id]
            logger.info(f"WebSocket连接已断开: {client_id}")
    
    async def send_message(self, client_id: str, message: dict):
        """发送消息到指定客户端"""
        if client_id in self.active_connections:
            websocket = self.active_connections[client_id]
            await websocket.send_text(json.dumps(message))

# 连接管理器实例
manager = ConnectionManager()

async def get_container_port(container_id: str) -> str:
    """
    获取容器端口（带缓存）
    """
    global container_cache, cache_timestamp

    current_time = asyncio.get_event_loop().time()

    # 检查缓存是否有效
    if current_time - cache_timestamp > CACHE_DURATION:
        # 刷新缓存
        containers = await docker_manager.list_algorithm_containers()
        container_cache = {c['name']: c for c in containers}
        cache_timestamp = current_time

    # 从缓存获取容器信息
    container = container_cache.get(container_id)
    if not container:
        raise HTTPException(status_code=404, detail=f"容器 {container_id} 未找到")

    if container['status'] != 'running':
        raise HTTPException(status_code=400, detail=f"容器 {container_id} 未运行")

    # 获取容器端口
    ports = container.get('ports', {})
    for container_port, host_port in ports.items():
        if container_port.startswith('8000'):
            return host_port

    raise HTTPException(status_code=400, detail=f"容器 {container_id} 端口未配置")

async def process_frame(container_id: str, frame_data: bytes, parameters: dict) -> dict:
    """
    处理视频帧数据（高性能版本）

    Args:
        container_id: 容器ID
        frame_data: 视频帧二进制数据
        parameters: 检测参数

    Returns:
        检测结果字典
    """
    try:
        # 快速获取容器端口（使用缓存）
        port = await get_container_port(container_id)

        # 构建API URL
        api_url = f"http://localhost:{port}/api/v1/detect"

        # 使用异步HTTP客户端
        async with aiohttp.ClientSession() as session:
            # 准备表单数据
            data = aiohttp.FormData()
            data.add_field('file', frame_data, filename='frame.jpg', content_type='image/jpeg')
            data.add_field('parameters', json.dumps(parameters))

            # 异步调用算法容器API
            async with session.post(api_url, data=data, timeout=aiohttp.ClientTimeout(total=3)) as response:
                if response.status == 200:
                    result = await response.json()
                    return {
                        'success': True,
                        'data': result,
                        'timestamp': asyncio.get_event_loop().time()
                    }
                else:
                    return {
                        'success': False,
                        'error': f"算法容器返回错误: {response.status}",
                        'timestamp': asyncio.get_event_loop().time()
                    }

    except asyncio.TimeoutError:
        return {
            'success': False,
            'error': "算法容器响应超时",
            'timestamp': asyncio.get_event_loop().time()
        }
    except Exception as e:
        logger.error(f"处理视频帧失败: {str(e)}")
        return {
            'success': False,
            'error': f"处理失败: {str(e)}",
            'timestamp': asyncio.get_event_loop().time()
        }

@router.websocket("/realtime-detect/{container_id}")
async def websocket_realtime_detect(websocket: WebSocket, container_id: str):
    """
    WebSocket实时检测端点
    
    Args:
        websocket: WebSocket连接
        container_id: 算法容器ID
    """
    client_id = f"{container_id}_{id(websocket)}"
    
    try:
        # 建立连接
        await manager.connect(websocket, client_id)
        
        # 发送连接成功消息
        await manager.send_message(client_id, {
            'type': 'connection',
            'status': 'connected',
            'message': f'WebSocket连接已建立，容器: {container_id}'
        })
        
        # 处理消息循环
        while True:
            try:
                # 接收消息
                message = await websocket.receive_text()
                data = json.loads(message)
                
                message_type = data.get('type')
                
                if message_type == 'frame':
                    # 处理视频帧数据
                    frame_base64 = data.get('frame')
                    parameters = data.get('parameters', {})
                    
                    if frame_base64:
                        # 解码base64图片数据
                        import base64
                        frame_data = base64.b64decode(frame_base64.split(',')[1] if ',' in frame_base64 else frame_base64)
                        
                        # 处理帧数据
                        result = await process_frame(container_id, frame_data, parameters)
                        
                        # 发送检测结果
                        await manager.send_message(client_id, {
                            'type': 'detection_result',
                            'result': result
                        })
                
                elif message_type == 'ping':
                    # 心跳检测
                    await manager.send_message(client_id, {
                        'type': 'pong',
                        'timestamp': asyncio.get_event_loop().time()
                    })
                
            except WebSocketDisconnect:
                logger.info(f"WebSocket客户端主动断开连接: {client_id}")
                break
            except json.JSONDecodeError:
                await manager.send_message(client_id, {
                    'type': 'error',
                    'error': '无效的JSON格式'
                })
            except Exception as e:
                logger.error(f"处理WebSocket消息失败: {str(e)}")
                await manager.send_message(client_id, {
                    'type': 'error',
                    'error': f'处理消息失败: {str(e)}'
                })
                
    except Exception as e:
        logger.error(f"WebSocket连接失败: {str(e)}")
    finally:
        # 清理连接
        manager.disconnect(client_id)

@router.get("/connections")
async def get_active_connections():
    """获取当前活跃的WebSocket连接"""
    return {
        'active_connections': len(manager.active_connections),
        'connections': list(manager.active_connections.keys())
    }
