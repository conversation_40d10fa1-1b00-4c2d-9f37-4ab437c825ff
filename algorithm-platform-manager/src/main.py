#!/usr/bin/env python3
"""
算法管理平台 - 主应用入口
专注于Docker容器管理、在线测试等功能
"""

import os
import sys
from pathlib import Path
from contextlib import asynccontextmanager
from typing import List

import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from pydantic_settings import BaseSettings

# 配置类
class Settings(BaseSettings):
    # API配置
    api_host: str = "0.0.0.0"
    api_port: int = 8100

    # CORS配置
    cors_origins: List[str] = ["http://localhost", "http://localhost:3000", "http://localhost:80"]
    cors_allow_credentials: bool = True
    cors_allow_methods: List[str] = ["*"]
    cors_allow_headers: List[str] = ["*"]

    # Redis配置
    redis_host: str = "redis"
    redis_port: int = 6379
    redis_db: int = 0

    # 应用配置
    app_name: str = "算法管理平台"
    app_version: str = "1.0.0"
    debug: bool = False

    class Config:
        env_file = ".env"

# 全局配置实例
settings = Settings()

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

# 导入API路由
from src.api.containers import router as containers_router
from src.api.testing import router as testing_router
from src.api.monitoring import router as monitoring_router
from src.api.websocket import router as websocket_router
from src.api.benchmark import router as benchmark_router

# 导入核心模块
from src.core.docker_manager import DockerManager
from src.database.database import init_database


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时初始化
    print("🚀 启动算法管理平台...")

    # 初始化数据库
    await init_database()

    # 初始化Docker管理器
    docker_manager = DockerManager()
    app.state.docker_manager = docker_manager

    # 检测现有算法容器
    containers = await docker_manager.list_algorithm_containers()
    print(f"🐳 发现 {len(containers)} 个算法容器")

    print("✅ 平台启动完成")

    yield

    # 关闭时清理
    print("🛑 关闭算法管理平台...")


# 创建FastAPI应用
app = FastAPI(
    title=settings.app_name,
    description="AI算法包统一管理平台 - 支持算法包检测、容器管理、在线测试",
    version=settings.app_version,
    docs_url="/api/docs",
    redoc_url="/api/redoc",
    lifespan=lifespan,
    debug=settings.debug
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=settings.cors_allow_credentials,
    allow_methods=settings.cors_allow_methods,
    allow_headers=settings.cors_allow_headers,
)

# 注册API路由
app.include_router(containers_router, prefix="/api/v1/containers", tags=["容器管理"])
app.include_router(testing_router, prefix="/api/v1/testing", tags=["在线测试"])
app.include_router(monitoring_router, prefix="/api/v1/monitoring", tags=["系统监控"])
app.include_router(websocket_router, prefix="/api/v1/ws", tags=["WebSocket实时检测"])
app.include_router(benchmark_router, prefix="/api/v1/benchmark", tags=["算法性能基准测试"])

# API根路径响应 (前后端分离，不提供静态文件服务)
@app.get("/")
async def root():
    return {
        "message": f"{settings.app_name} API 服务",
        "version": settings.app_version,
        "docs": "/api/docs",
        "health": "/api/health",
        "architecture": "前后端分离架构"
    }


@app.get("/api/health")
async def health_check():
    """健康检查接口"""
    return {
        "status": "healthy",
        "service": "算法管理平台",
        "version": "1.0.0",
        "timestamp": "2025-07-29"
    }


@app.get("/api/info")
async def get_platform_info():
    """获取平台信息"""
    return {
        "name": "算法管理平台",
        "version": "1.0.0",
        "description": "AI算法包统一管理平台",
        "features": [
            "Docker容器检测",
            "容器生命周期管理",
            "在线测试平台",
            "实时监控面板",
            "Web管理界面"
        ],
        "supported_containers": [
            "带有 algorithm.platform=true 标签的容器"
        ]
    }


if __name__ == "__main__":
    # 开发环境启动
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8100,
        reload=True,
        log_level="info"
    )
