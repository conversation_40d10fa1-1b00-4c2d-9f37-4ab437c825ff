services:
  # 人车非检测算法 - v2.0
  renchefei-v2:
    build:
      context: ./algorithms/renchefei
      dockerfile: Dockerfile
    image: renchefei:2.0.0
    container_name: renchefei-v2
    ports:
      - "8002:8000"
    environment:
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
    volumes:
      - ./data/renchefei/input:/app/data/input
      - ./data/renchefei/output:/app/data/output
      - ./logs/renchefei:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    labels:
      - "algorithm.platform=true"
      - "algorithm.name=人车非检测算法"
      - "algorithm.type=目标检测"
      - "algorithm.version=2.0.0"
      - "algorithm.port=8002"

  # 温州人脸识别算法 - v2.0
  wenzhou-face-v2:
    build:
      context: ./algorithms/wenzhou_face
      dockerfile: Dockerfile
    image: wenzhou-face:2.0.0
    container_name: wenzhou-face-v2
    ports:
      - "8003:8001"
    environment:
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
    volumes:
      - ./data/wenzhou_face/input:/app/data/input
      - ./data/wenzhou_face/output:/app/data/output
      - ./logs/wenzhou_face:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    labels:
      - "algorithm.platform=true"
      - "algorithm.name=温州人脸识别算法"
      - "algorithm.type=人脸识别"
      - "algorithm.version=2.0.0"
      - "algorithm.port=8003"

  # 交通事故分类算法 - v2.0
  accident-classify-v2:
    build:
      context: ./algorithms/accident_classify
      dockerfile: Dockerfile
    image: accident-classify:2.0.0
    container_name: accident-classify-v2
    ports:
      - "8004:8003"
    environment:
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
    volumes:
      - ./data/accident_classify/input:/app/data/input
      - ./data/accident_classify/output:/app/data/output
      - ./logs/accident_classify:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8003/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    labels:
      - "algorithm.platform=true"
      - "algorithm.name=交通事故分类算法"
      - "algorithm.type=图像分类"
      - "algorithm.version=2.0.0"
      - "algorithm.port=8004"

networks:
  default:
    name: algorithm-network
    driver: bridge

volumes:
  renchefei_data:
    driver: local
  wenzhou_face_data:
    driver: local
  accident_classify_data:
    driver: local
